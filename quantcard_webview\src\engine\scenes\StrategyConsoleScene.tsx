/**
 * 🎮 策略控制台场景 - 移动端游戏风格
 * 集成我的策略、监控室、实验室三大功能模块
 * 支持流畅的页面切换和移动端优化的交互体验
 */

import React, { useState, useCallback } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import type { PanInfo } from 'framer-motion'
import { useTheme } from '../../styles/themes/ThemeProvider'
import UnifiedMobileNav from '../components/ui/Nav/UnifiedMobileNav'

// 导入页面组件（稍后创建）
import MyStrategiesPage from '../components/StrategyConsole/MyStrategiesPage'
import MonitoringRoomPage from '../components/StrategyConsole/MonitoringRoomPage'
import LabPage from '../components/StrategyConsole/LabPage'

// 🎯 页面类型定义
type ConsolePage = 'strategies' | 'monitoring' | 'lab'

// 🎨 样式组件
const SceneContainer = styled.div<{ theme: any }>`
  position: relative;
  width: 100vw;
  height: 100vh;
  background: ${props => props.theme.colors.background};
  overflow: hidden;
  display: flex;
  flex-direction: column;
`

const Header = styled.div<{ theme: any }>`
  position: relative;
  z-index: 100;
  padding: 1rem;
  background: ${props => props.theme.colors.surface};
  border-bottom: 1px solid ${props => props.theme.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
`

const HeaderTitle = styled.h1<{ theme: any }>`
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  text-align: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
`

const TabNavigation = styled.div<{ theme: any }>`
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  background: ${props => props.theme.colors.background};
  border-radius: 12px;
  padding: 4px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;

  /* 移动端优化 */
  @media (max-width: 768px) {
    margin: 0.75rem -0.5rem 0;
    border-radius: 8px;
    padding: 3px;
  }
`

const TabIndicator = styled(motion.div)<{ theme: any }>`
  position: absolute;
  top: 4px;
  bottom: 4px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  z-index: 1;

  @media (max-width: 768px) {
    top: 3px;
    bottom: 3px;
    border-radius: 6px;
  }
`

const TabButton = styled(motion.button)<{ $active: boolean; theme: any }>`
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
  background: transparent;

  color: ${props => props.$active
    ? 'white'
    : props.theme.colors.textSecondary};

  /* 移动端优化 */
  @media (max-width: 768px) {
    padding: 0.6rem 0.75rem;
    font-size: 0.85rem;
    min-height: 44px; /* iOS推荐的最小触摸目标 */
  }

  /* 触摸反馈 */
  &:active {
    transform: scale(0.98);
  }

  /* 无障碍支持 */
  &:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
  }
`

const ContentArea = styled.div`
  flex: 1;
  position: relative;
  overflow: hidden;
  touch-action: pan-y; /* 允许垂直滚动，限制水平滚动用于手势 */
`

const PageContainer = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;

  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
`

// 🎮 页面配置
const PAGES: Array<{
  key: ConsolePage
  label: string
  icon: string
  component: React.ComponentType<any>
}> = [
  {
    key: 'strategies',
    label: '我的策略',
    icon: '📊',
    component: MyStrategiesPage
  },
  {
    key: 'monitoring',
    label: '监控室',
    icon: '📡',
    component: MonitoringRoomPage
  },
  {
    key: 'lab',
    label: '实验室',
    icon: '🧪',
    component: LabPage
  }
]

// 🎯 策略控制台主组件
function StrategyConsoleScene() {
  const { theme } = useTheme()
  const [currentPage, setCurrentPage] = useState<ConsolePage>('strategies')
  const [isTransitioning, setIsTransitioning] = useState(false)

  // 🔄 页面切换处理
  const handlePageChange = useCallback(async (page: ConsolePage) => {
    if (page === currentPage || isTransitioning) return

    setIsTransitioning(true)

    // 添加切换延迟以确保动画流畅
    await new Promise(resolve => setTimeout(resolve, 150))

    setCurrentPage(page)
    setIsTransitioning(false)
  }, [currentPage, isTransitioning])

  // 🎯 手势滑动切换
  const handleSwipe = useCallback((direction: 'left' | 'right') => {
    const currentIndex = PAGES.findIndex(p => p.key === currentPage)
    let nextIndex: number

    if (direction === 'left' && currentIndex < PAGES.length - 1) {
      nextIndex = currentIndex + 1
    } else if (direction === 'right' && currentIndex > 0) {
      nextIndex = currentIndex - 1
    } else {
      return // 无法切换
    }

    handlePageChange(PAGES[nextIndex].key)
  }, [currentPage, handlePageChange])

  // 🎮 拖拽结束处理
  const handleDragEnd = useCallback((_: any, info: PanInfo) => {
    const threshold = 50 // 滑动阈值
    const velocity = Math.abs(info.velocity.x)
    const offset = info.offset.x

    // 根据滑动距离和速度判断是否切换页面
    if (Math.abs(offset) > threshold || velocity > 500) {
      if (offset > 0) {
        handleSwipe('right') // 向右滑动，切换到上一页
      } else {
        handleSwipe('left') // 向左滑动，切换到下一页
      }
    }
  }, [handleSwipe])

  // 🎨 页面切换动画配置
  const pageVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? '100%' : '-100%',
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? '100%' : '-100%',
      opacity: 0
    })
  }

  const pageTransition = {
    type: 'tween' as const,
    ease: 'anticipate' as const,
    duration: 0.4
  }

  // 🎯 获取当前页面组件
  const getCurrentPageComponent = () => {
    const page = PAGES.find(p => p.key === currentPage)
    return page?.component || MyStrategiesPage
  }

  const CurrentPageComponent = getCurrentPageComponent()

  return (
    <SceneContainer theme={theme}>
      {/* 顶部导航 */}
      <UnifiedMobileNav title="策略控制台" />
      
      {/* 页面头部 */}
      <Header theme={theme}>
        <HeaderTitle theme={theme}>策略控制台</HeaderTitle>
        
        {/* 标签页导航 */}
        <TabNavigation theme={theme}>
          {PAGES.map((page) => (
            <TabButton
              key={page.key}
              $active={currentPage === page.key}
              theme={theme}
              onClick={() => handlePageChange(page.key)}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              disabled={isTransitioning}
            >
              <span style={{ marginRight: '0.5rem' }}>{page.icon}</span>
              {page.label}
            </TabButton>
          ))}
        </TabNavigation>
      </Header>

      {/* 内容区域 */}
      <ContentArea>
        <AnimatePresence mode="wait">
          <PageContainer
            key={currentPage}
            custom={0}
            variants={pageVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={pageTransition}
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragEnd={handleDragEnd}
          >
            <CurrentPageComponent />
          </PageContainer>
        </AnimatePresence>
      </ContentArea>
    </SceneContainer>
  )
}

export default StrategyConsoleScene
