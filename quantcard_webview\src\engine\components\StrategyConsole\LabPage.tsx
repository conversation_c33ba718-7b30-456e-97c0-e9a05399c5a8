/**
 * 🧪 实验室页面 - 移动端游戏风格
 * 基于现有的StrategyOptimizationScene，保持策略优化和分析功能
 * 集成数据源、智能调优、图表可视化、全面分析的游戏化界面
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react'
import styled from 'styled-components'
import { motion } from 'framer-motion'
import { useUIState } from '../../../store/hooks'
import { useTheme } from '../../../styles/themes/ThemeProvider'

import type { StrategyTemplate, PerformanceMetrics } from '../../../types/game'
import CyberButton from '../../components/CyberButton'

interface StrategyConfig extends StrategyTemplate {
  type: 'filter' | 'timing' | 'backtest'
  performance?: PerformanceMetrics
}

// 🎨 样式组件
const PageContainer = styled.div<{ theme: any }>`
  padding: 1rem;
  min-height: 100%;
  background: ${props => props.theme.colors.background};
`

const HeaderSection = styled.div`
  margin-bottom: 1.5rem;
`

const PageTitle = styled.h2<{ theme: any }>`
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const PageSubtitle = styled.p<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const LabContent = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
`

const SectionCard = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.surface};
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid ${props => props.theme.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
`

const SectionTitle = styled.h3<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const FeatureGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
`

const FeatureCard = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.background};
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
  }
`

const FeatureIcon = styled.div`
  font-size: 2rem;
  margin-bottom: 0.75rem;
`

const FeatureTitle = styled.h4<{ theme: any }>`
  margin: 0 0 0.5rem 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const FeatureDescription = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const ComingSoonBadge = styled.span<{ theme: any }>`
  position: absolute;
  top: 1rem;
  right: 1rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: #f59e0b20;
  color: #f59e0b;
  font-size: 0.7rem;
  font-weight: 500;
`

// 🎮 实验室页面组件
function LabPage() {
  const { theme } = useTheme()
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null)

  // 🧪 实验室功能配置
  const labFeatures = [
    {
      id: 'strategy_optimizer',
      icon: '🎯',
      title: '策略优化器',
      description: '使用AI算法自动优化策略参数，提升策略表现',
      available: true
    },
    {
      id: 'backtest_engine',
      icon: '📈',
      title: '回测引擎',
      description: '历史数据回测，验证策略在不同市场环境下的表现',
      available: true
    },
    {
      id: 'risk_analyzer',
      icon: '🛡️',
      title: '风险分析器',
      description: '深度分析策略风险特征，提供风险控制建议',
      available: false
    },
    {
      id: 'signal_simulator',
      icon: '⚡',
      title: '信号模拟器',
      description: '模拟策略信号生成过程，调试策略逻辑',
      available: false
    },
    {
      id: 'market_scanner',
      icon: '🔍',
      title: '市场扫描器',
      description: '实时扫描市场机会，发现潜在的交易信号',
      available: false
    },
    {
      id: 'portfolio_builder',
      icon: '🏗️',
      title: '组合构建器',
      description: '构建多策略投资组合，优化资产配置',
      available: false
    }
  ]

  // 🎯 处理功能选择
  const handleFeatureSelect = useCallback((featureId: string) => {
    const feature = labFeatures.find(f => f.id === featureId)
    if (!feature?.available) {
      console.log('功能暂未开放:', featureId)
      return
    }
    
    setSelectedFeature(featureId)
    console.log('选择实验室功能:', featureId)
    
    // TODO: 根据选择的功能打开对应的界面
    switch (featureId) {
      case 'strategy_optimizer':
        // 打开策略优化器
        break
      case 'backtest_engine':
        // 打开回测引擎
        break
      default:
        break
    }
  }, [])

  return (
    <PageContainer theme={theme}>
      {/* 页面头部 */}
      <HeaderSection>
        <PageTitle theme={theme}>
          🧪 实验室
        </PageTitle>
        <PageSubtitle theme={theme}>
          探索高级策略分析工具，优化您的投资策略
        </PageSubtitle>
      </HeaderSection>

      {/* 实验室内容 */}
      <LabContent>
        {/* 核心功能区 */}
        <SectionCard
          theme={theme}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <SectionTitle theme={theme}>
            🔬 核心功能
          </SectionTitle>
          
          <FeatureGrid>
            {labFeatures.map((feature, index) => (
              <FeatureCard
                key={feature.id}
                theme={theme}
                onClick={() => handleFeatureSelect(feature.id)}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 + index * 0.1 }}
                style={{ 
                  opacity: feature.available ? 1 : 0.6,
                  position: 'relative'
                }}
              >
                {!feature.available && (
                  <ComingSoonBadge theme={theme}>即将推出</ComingSoonBadge>
                )}
                
                <FeatureIcon>{feature.icon}</FeatureIcon>
                <FeatureTitle theme={theme}>{feature.title}</FeatureTitle>
                <FeatureDescription theme={theme}>
                  {feature.description}
                </FeatureDescription>
              </FeatureCard>
            ))}
          </FeatureGrid>
        </SectionCard>

        {/* 快速操作区 */}
        <SectionCard
          theme={theme}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <SectionTitle theme={theme}>
            ⚡ 快速操作
          </SectionTitle>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
            <CyberButton
              variant="primary"
              size="large"
              onClick={() => console.log('创建新策略')}
              style={{ width: '100%' }}
            >
              🎯 创建新策略
            </CyberButton>
            
            <CyberButton
              variant="secondary"
              size="large"
              onClick={() => console.log('导入策略模板')}
              style={{ width: '100%' }}
            >
              📥 导入策略模板
            </CyberButton>
            
            <CyberButton
              variant="secondary"
              size="large"
              onClick={() => console.log('策略市场')}
              style={{ width: '100%' }}
            >
              🏪 策略市场
            </CyberButton>
          </div>
        </SectionCard>
      </LabContent>
    </PageContainer>
  )
}

export default LabPage
