/**
 * 📊 我的策略页面 - 移动端游戏风格
 * 展示和管理用户的策略组，支持执行、编辑、删除等操作
 * 基于backup/old_frontend的策略管理页面，优化为移动端体验
 */

import React, { useState, useEffect, useCallback } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useTheme } from '../../../styles/themes/ThemeProvider'
import { useStrategyState } from '../../../store/hooks'
import type { StrategyGroup } from '../../../types/game'

// 🎨 样式组件
const PageContainer = styled.div<{ theme: any }>`
  padding: 1rem;
  min-height: 100%;
  background: ${props => props.theme.colors.background};
`

const HeaderSection = styled.div`
  margin-bottom: 1.5rem;
`

const PageTitle = styled.h2<{ theme: any }>`
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
`

const PageSubtitle = styled.p<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const ActionBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  gap: 1rem;
`

const CreateButton = styled(motion.button)<{ theme: any }>`
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  
  display: flex;
  align-items: center;
  gap: 0.5rem;
  
  &:active {
    transform: scale(0.98);
  }
`

const FilterSection = styled.div`
  display: flex;
  gap: 0.5rem;
  overflow-x: auto;
  padding-bottom: 0.5rem;
  
  &::-webkit-scrollbar {
    display: none;
  }
`

const FilterChip = styled(motion.button)<{ $active: boolean; theme: any }>`
  padding: 0.5rem 1rem;
  border: 1px solid ${props => props.$active ? '#667eea' : props.theme.colors.border};
  border-radius: 20px;
  background: ${props => props.$active ? '#667eea' : props.theme.colors.surface};
  color: ${props => props.$active ? 'white' : props.theme.colors.text};
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #667eea;
  }
`

const StrategiesList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const StrategyCard = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.surface};
  border-radius: 16px;
  padding: 1.25rem;
  border: 1px solid ${props => props.theme.colors.border};
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
`

const StrategyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
`

const StrategyInfo = styled.div`
  flex: 1;
`

const StrategyName = styled.h3<{ theme: any }>`
  margin: 0 0 0.25rem 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const StrategyMeta = styled.div`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
`

const MetaBadge = styled.span<{ $color: string; theme: any }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: ${props => props.$color}20;
  color: ${props => props.$color};
  font-size: 0.75rem;
  font-weight: 500;
`

const StatusBadge = styled(MetaBadge)<{ $status: 'active' | 'inactive' }>`
  background: ${props => props.$status === 'active' ? '#10b98120' : '#6b728020'};
  color: ${props => props.$status === 'active' ? '#10b981' : '#6b7280'};
`

const StrategyDescription = styled.p<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 0.85rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const CardsList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`

const CardChip = styled.span<{ theme: any }>`
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  background: ${props => props.theme.colors.background};
  color: ${props => props.theme.colors.text};
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid ${props => props.theme.colors.border};
`

const ActionButtons = styled.div`
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
`

const ActionButton = styled(motion.button)<{ $variant: 'primary' | 'secondary' | 'danger'; theme: any }>`
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  
  ${props => {
    switch (props.$variant) {
      case 'primary':
        return `
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
        `
      case 'secondary':
        return `
          background: ${props.theme.colors.background};
          color: ${props.theme.colors.text};
          border: 1px solid ${props.theme.colors.border};
        `
      case 'danger':
        return `
          background: #ef444420;
          color: #ef4444;
        `
      default:
        return ''
    }
  }}
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`

const EmptyState = styled.div<{ theme: any }>`
  text-align: center;
  padding: 3rem 1rem;
  color: ${props => props.theme.colors.textSecondary};
`

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`

const EmptyText = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  color: ${props => props.theme.colors.textSecondary};
`

// 🎯 筛选类型
type FilterType = 'all' | 'active' | 'inactive' | 'timing' | 'filter'

// 🎮 我的策略页面组件
function MyStrategiesPage() {
  const { theme } = useTheme()
  const { groups, loading, loadGroups } = useStrategyState()
  const [filter, setFilter] = useState<FilterType>('all')
  const [executingIds, setExecutingIds] = useState<Set<string>>(new Set())

  // 🔄 加载策略组
  useEffect(() => {
    loadGroups()
  }, [loadGroups])

  // 🎯 筛选策略组
  const filteredGroups = groups.filter(group => {
    switch (filter) {
      case 'active':
        return group.status === 'active'
      case 'inactive':
        return group.status === 'inactive'
      case 'timing':
        return group.group_type === 'timing'
      case 'filter':
        return group.group_type === 'filter'
      default:
        return true
    }
  })

  // 🎮 处理策略执行
  const handleExecuteStrategy = useCallback(async (strategyId: string) => {
    setExecutingIds(prev => new Set(prev).add(strategyId))
    
    try {
      // TODO: 调用策略执行API
      console.log('执行策略:', strategyId)
      
      // 模拟执行延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
    } catch (error) {
      console.error('策略执行失败:', error)
    } finally {
      setExecutingIds(prev => {
        const newSet = new Set(prev)
        newSet.delete(strategyId)
        return newSet
      })
    }
  }, [])

  // 🎨 筛选选项
  const filterOptions: Array<{ key: FilterType; label: string; icon: string }> = [
    { key: 'all', label: '全部', icon: '📊' },
    { key: 'active', label: '运行中', icon: '🟢' },
    { key: 'inactive', label: '已停止', icon: '⚪' },
    { key: 'timing', label: '择时', icon: '⏰' },
    { key: 'filter', label: '选股', icon: '🎯' }
  ]

  return (
    <PageContainer theme={theme}>
      {/* 页面头部 */}
      <HeaderSection>
        <PageTitle theme={theme}>我的策略</PageTitle>
        <PageSubtitle theme={theme}>
          管理您创建的所有策略组合，监控它们的表现并进行优化
        </PageSubtitle>
        
        {/* 操作栏 */}
        <ActionBar>
          <CreateButton
            theme={theme}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => {
              // TODO: 打开创建策略模态框
              console.log('创建新策略')
            }}
          >
            <span>➕</span>
            创建策略
          </CreateButton>
        </ActionBar>
        
        {/* 筛选器 */}
        <FilterSection>
          {filterOptions.map(option => (
            <FilterChip
              key={option.key}
              $active={filter === option.key}
              theme={theme}
              onClick={() => setFilter(option.key)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span style={{ marginRight: '0.25rem' }}>{option.icon}</span>
              {option.label}
            </FilterChip>
          ))}
        </FilterSection>
      </HeaderSection>

      {/* 策略列表 */}
      {loading ? (
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div>加载中...</div>
        </div>
      ) : filteredGroups.length === 0 ? (
        <EmptyState theme={theme}>
          <EmptyIcon>📊</EmptyIcon>
          <EmptyText theme={theme}>
            {filter === 'all' ? '暂无策略组合' : '没有符合条件的策略'}
          </EmptyText>
        </EmptyState>
      ) : (
        <StrategiesList>
          <AnimatePresence>
            {filteredGroups.map((strategy, index) => (
              <StrategyCard
                key={strategy.id}
                theme={theme}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ scale: 1.01 }}
              >
                {/* 策略头部信息 */}
                <StrategyHeader>
                  <StrategyInfo>
                    <StrategyName theme={theme}>{strategy.name}</StrategyName>
                    <StrategyMeta>
                      <StatusBadge $status={strategy.status} theme={theme}>
                        {strategy.status === 'active' ? '运行中' : '已停止'}
                      </StatusBadge>
                      <MetaBadge 
                        $color={strategy.group_type === 'timing' ? '#f59e0b' : '#3b82f6'} 
                        theme={theme}
                      >
                        {strategy.group_type === 'timing' ? '择时策略' : '选股策略'}
                      </MetaBadge>
                      <MetaBadge $color="#6b7280" theme={theme}>
                        {strategy.cards?.length || 0} 张卡片
                      </MetaBadge>
                    </StrategyMeta>
                  </StrategyInfo>
                </StrategyHeader>

                {/* 策略描述 */}
                {strategy.description && (
                  <StrategyDescription theme={theme}>
                    {strategy.description}
                  </StrategyDescription>
                )}

                {/* 卡片列表 */}
                {strategy.cards && strategy.cards.length > 0 && (
                  <CardsList>
                    {strategy.cards.slice(0, 3).map((card, cardIndex) => (
                      <CardChip key={cardIndex} theme={theme}>
                        {card.name || card.template_name || `卡片${cardIndex + 1}`}
                      </CardChip>
                    ))}
                    {strategy.cards.length > 3 && (
                      <CardChip theme={theme}>
                        +{strategy.cards.length - 3} 更多
                      </CardChip>
                    )}
                  </CardsList>
                )}

                {/* 操作按钮 */}
                <ActionButtons>
                  <ActionButton
                    $variant="secondary"
                    theme={theme}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      // TODO: 编辑策略
                      console.log('编辑策略:', strategy.id)
                    }}
                  >
                    编辑
                  </ActionButton>
                  <ActionButton
                    $variant="primary"
                    theme={theme}
                    disabled={executingIds.has(strategy.id)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => handleExecuteStrategy(strategy.id)}
                  >
                    {executingIds.has(strategy.id) ? '执行中...' : 
                     strategy.status === 'active' ? '停止' : '执行'}
                  </ActionButton>
                  <ActionButton
                    $variant="danger"
                    theme={theme}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => {
                      // TODO: 删除策略
                      if (confirm('确定要删除这个策略组吗？')) {
                        console.log('删除策略:', strategy.id)
                      }
                    }}
                  >
                    删除
                  </ActionButton>
                </ActionButtons>
              </StrategyCard>
            ))}
          </AnimatePresence>
        </StrategiesList>
      )}
    </PageContainer>
  )
}

export default MyStrategiesPage
