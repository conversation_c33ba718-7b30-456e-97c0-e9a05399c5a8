/**
 * 📡 监控室页面 - 移动端游戏风格
 * 实时监控策略组的执行状态、性能指标和运行日志
 * 提供直观的数据可视化和实时更新功能
 */

import React, { useState, useEffect, useCallback } from 'react'
import styled from 'styled-components'
import { motion, AnimatePresence } from 'framer-motion'
import { useTheme } from '../../../styles/themes/ThemeProvider'
import { useStrategyState } from '../../../store/hooks'

// 🎨 样式组件
const PageContainer = styled.div<{ theme: any }>`
  padding: 1rem;
  min-height: 100%;
  background: ${props => props.theme.colors.background};
`

const HeaderSection = styled.div`
  margin-bottom: 1.5rem;
`

const PageTitle = styled.h2<{ theme: any }>`
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const StatusIndicator = styled(motion.div)<{ $status: 'online' | 'offline' }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.$status === 'online' ? '#10b981' : '#ef4444'};
  box-shadow: 0 0 8px ${props => props.$status === 'online' ? '#10b98150' : '#ef444450'};
`

const PageSubtitle = styled.p<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  color: ${props => props.theme.colors.textSecondary};
  line-height: 1.4;
`

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
`

const StatCard = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.surface};
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  text-align: center;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  }
`

const StatValue = styled.div<{ theme: any }>`
  font-size: 1.5rem;
  font-weight: 700;
  color: ${props => props.theme.colors.text};
  margin-bottom: 0.25rem;
`

const StatLabel = styled.div<{ theme: any }>`
  font-size: 0.8rem;
  color: ${props => props.theme.colors.textSecondary};
  font-weight: 500;
`

const MonitoringSection = styled.div`
  margin-bottom: 1.5rem;
`

const SectionTitle = styled.h3<{ theme: any }>`
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
  display: flex;
  align-items: center;
  gap: 0.5rem;
`

const StrategyMonitorList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`

const StrategyMonitorCard = styled(motion.div)<{ theme: any }>`
  background: ${props => props.theme.colors.surface};
  border-radius: 12px;
  padding: 1rem;
  border: 1px solid ${props => props.theme.colors.border};
  position: relative;
`

const StrategyHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`

const StrategyName = styled.h4<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: ${props => props.theme.colors.text};
`

const StrategyStatus = styled.span<{ $status: 'running' | 'stopped' | 'error'; theme: any }>`
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  
  ${props => {
    switch (props.$status) {
      case 'running':
        return `
          background: #10b98120;
          color: #10b981;
        `
      case 'stopped':
        return `
          background: #6b728020;
          color: #6b7280;
        `
      case 'error':
        return `
          background: #ef444420;
          color: #ef4444;
        `
      default:
        return ''
    }
  }}
`

const MetricsRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 0.75rem;
  margin-bottom: 0.75rem;
`

const MetricItem = styled.div<{ theme: any }>`
  text-align: center;
`

const MetricValue = styled.div<{ $color?: string; theme: any }>`
  font-size: 0.9rem;
  font-weight: 600;
  color: ${props => props.$color || props.theme.colors.text};
  margin-bottom: 0.25rem;
`

const MetricLabel = styled.div<{ theme: any }>`
  font-size: 0.7rem;
  color: ${props => props.theme.colors.textSecondary};
`

const LogSection = styled.div<{ theme: any }>`
  background: ${props => props.theme.colors.background};
  border-radius: 8px;
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.colors.border};
  max-height: 120px;
  overflow-y: auto;
`

const LogEntry = styled.div<{ $level: 'info' | 'warning' | 'error'; theme: any }>`
  font-size: 0.75rem;
  font-family: 'Monaco', 'Menlo', monospace;
  margin-bottom: 0.25rem;
  padding: 0.25rem 0;
  
  color: ${props => {
    switch (props.$level) {
      case 'error':
        return '#ef4444'
      case 'warning':
        return '#f59e0b'
      default:
        return props.theme.colors.textSecondary
    }
  }};
  
  &:last-child {
    margin-bottom: 0;
  }
`

const EmptyState = styled.div<{ theme: any }>`
  text-align: center;
  padding: 3rem 1rem;
  color: ${props => props.theme.colors.textSecondary};
`

const EmptyIcon = styled.div`
  font-size: 3rem;
  margin-bottom: 1rem;
`

const EmptyText = styled.p<{ theme: any }>`
  margin: 0;
  font-size: 1rem;
  color: ${props => props.theme.colors.textSecondary};
`

// 🎯 监控数据类型
interface MonitoringData {
  totalStrategies: number
  activeStrategies: number
  totalSignals: number
  successRate: number
  strategies: Array<{
    id: string
    name: string
    status: 'running' | 'stopped' | 'error'
    performance: {
      return: number
      winRate: number
      signals: number
      lastUpdate: string
    }
    logs: Array<{
      level: 'info' | 'warning' | 'error'
      message: string
      timestamp: string
    }>
  }>
}

// 🎮 监控室页面组件
function MonitoringRoomPage() {
  const { theme } = useTheme()
  const { groups } = useStrategyState()
  const [monitoringData, setMonitoringData] = useState<MonitoringData | null>(null)
  const [isOnline, setIsOnline] = useState(true)

  // 🔄 模拟实时数据更新
  useEffect(() => {
    const updateMonitoringData = () => {
      const activeGroups = groups.filter(g => g.status === 'active')
      
      const mockData: MonitoringData = {
        totalStrategies: groups.length,
        activeStrategies: activeGroups.length,
        totalSignals: Math.floor(Math.random() * 100) + 50,
        successRate: Math.random() * 30 + 70, // 70-100%
        strategies: activeGroups.slice(0, 5).map(group => ({
          id: group.id,
          name: group.name,
          status: Math.random() > 0.1 ? 'running' : (Math.random() > 0.5 ? 'stopped' : 'error'),
          performance: {
            return: (Math.random() - 0.5) * 20, // -10% to +10%
            winRate: Math.random() * 40 + 50, // 50-90%
            signals: Math.floor(Math.random() * 20) + 5,
            lastUpdate: new Date().toLocaleTimeString()
          },
          logs: [
            {
              level: 'info',
              message: `策略执行完成，生成${Math.floor(Math.random() * 5) + 1}个信号`,
              timestamp: new Date().toLocaleTimeString()
            },
            {
              level: Math.random() > 0.8 ? 'warning' : 'info',
              message: Math.random() > 0.8 ? '数据获取延迟' : '数据更新成功',
              timestamp: new Date(Date.now() - Math.random() * 60000).toLocaleTimeString()
            }
          ]
        }))
      }
      
      setMonitoringData(mockData)
    }

    // 初始加载
    updateMonitoringData()
    
    // 定时更新（每5秒）
    const interval = setInterval(updateMonitoringData, 5000)
    
    return () => clearInterval(interval)
  }, [groups])

  // 🎨 状态指示器动画
  const statusVariants = {
    online: {
      scale: [1, 1.2, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    },
    offline: {
      scale: 1
    }
  }

  if (!monitoringData) {
    return (
      <PageContainer theme={theme}>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <div>加载监控数据...</div>
        </div>
      </PageContainer>
    )
  }

  return (
    <PageContainer theme={theme}>
      {/* 页面头部 */}
      <HeaderSection>
        <PageTitle theme={theme}>
          📡 监控室
          <StatusIndicator
            $status={isOnline ? 'online' : 'offline'}
            variants={statusVariants}
            animate={isOnline ? 'online' : 'offline'}
          />
        </PageTitle>
        <PageSubtitle theme={theme}>
          实时监控策略组的执行状态和性能表现
        </PageSubtitle>
      </HeaderSection>

      {/* 统计概览 */}
      <StatsGrid>
        <StatCard
          theme={theme}
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <StatValue theme={theme}>{monitoringData.totalStrategies}</StatValue>
          <StatLabel theme={theme}>总策略数</StatLabel>
        </StatCard>
        
        <StatCard
          theme={theme}
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <StatValue theme={theme}>{monitoringData.activeStrategies}</StatValue>
          <StatLabel theme={theme}>运行中</StatLabel>
        </StatCard>
        
        <StatCard
          theme={theme}
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <StatValue theme={theme}>{monitoringData.totalSignals}</StatValue>
          <StatLabel theme={theme}>今日信号</StatLabel>
        </StatCard>
        
        <StatCard
          theme={theme}
          whileHover={{ scale: 1.02 }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          <StatValue theme={theme}>{monitoringData.successRate.toFixed(1)}%</StatValue>
          <StatLabel theme={theme}>成功率</StatLabel>
        </StatCard>
      </StatsGrid>

      {/* 策略监控 */}
      <MonitoringSection>
        <SectionTitle theme={theme}>
          🎯 策略监控
        </SectionTitle>
        
        {monitoringData.strategies.length === 0 ? (
          <EmptyState theme={theme}>
            <EmptyIcon>📊</EmptyIcon>
            <EmptyText theme={theme}>暂无运行中的策略</EmptyText>
          </EmptyState>
        ) : (
          <StrategyMonitorList>
            <AnimatePresence>
              {monitoringData.strategies.map((strategy, index) => (
                <StrategyMonitorCard
                  key={strategy.id}
                  theme={theme}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ scale: 1.01 }}
                >
                  {/* 策略头部 */}
                  <StrategyHeader>
                    <StrategyName theme={theme}>{strategy.name}</StrategyName>
                    <StrategyStatus $status={strategy.status} theme={theme}>
                      {strategy.status === 'running' ? '运行中' : 
                       strategy.status === 'stopped' ? '已停止' : '错误'}
                    </StrategyStatus>
                  </StrategyHeader>

                  {/* 性能指标 */}
                  <MetricsRow>
                    <MetricItem theme={theme}>
                      <MetricValue 
                        $color={strategy.performance.return >= 0 ? '#10b981' : '#ef4444'} 
                        theme={theme}
                      >
                        {strategy.performance.return >= 0 ? '+' : ''}{strategy.performance.return.toFixed(2)}%
                      </MetricValue>
                      <MetricLabel theme={theme}>收益率</MetricLabel>
                    </MetricItem>
                    
                    <MetricItem theme={theme}>
                      <MetricValue theme={theme}>
                        {strategy.performance.winRate.toFixed(1)}%
                      </MetricValue>
                      <MetricLabel theme={theme}>胜率</MetricLabel>
                    </MetricItem>
                    
                    <MetricItem theme={theme}>
                      <MetricValue theme={theme}>
                        {strategy.performance.signals}
                      </MetricValue>
                      <MetricLabel theme={theme}>信号数</MetricLabel>
                    </MetricItem>
                    
                    <MetricItem theme={theme}>
                      <MetricValue theme={theme}>
                        {strategy.performance.lastUpdate}
                      </MetricValue>
                      <MetricLabel theme={theme}>最后更新</MetricLabel>
                    </MetricItem>
                  </MetricsRow>

                  {/* 运行日志 */}
                  <LogSection theme={theme}>
                    {strategy.logs.map((log, logIndex) => (
                      <LogEntry key={logIndex} $level={log.level} theme={theme}>
                        [{log.timestamp}] {log.message}
                      </LogEntry>
                    ))}
                  </LogSection>
                </StrategyMonitorCard>
              ))}
            </AnimatePresence>
          </StrategyMonitorList>
        )}
      </MonitoringSection>
    </PageContainer>
  )
}

export default MonitoringRoomPage
